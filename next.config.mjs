
/** @type {import('next').NextConfig} */
const nextConfig = {
    output: "standalone", // for self-hosting

    // basePath와 assetPrefix 분리 설정
    basePath: process.env.NEXT_BASE_PATH || '',
    assetPrefix: process.env.NEXT_ASSET_PREFIX || process.env.NEXT_BASE_PATH || '',

    eslint: { ignoreDuringBuilds: true },

    // 런타임 노출: 클라이언트/미들웨어에서 동일 값 사용
    env: {
        NEXT_PUBLIC_BASE_PATH: process.env.NEXT_BASE_PATH || '',
    },
    pageExtensions: ['js', 'jsx', 'mdx', 'ts', 'tsx'],

};

export default nextConfig

