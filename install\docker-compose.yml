services:
  # Next.js 챗봇 애플리케이션
  lx-chatbot:
    image: lx-chatbot/frontend:latest
    container_name: lx-chatbot-frontend
    restart: unless-stopped
    environment:
      # 공유재산 관리 어시스턴트 설정
      - DIFY_APP_KEY=app-R7qR0AIjAE8boUf3XsBXTBiK
      - DIFY_URL=http://121.163.19.104:888/v1
    # 외부 포트 노출 제거 (nginx를 통해서만 접근)
    expose:
      - "3000"
    networks:
      - chatbot-network

  # Nginx 프록시 서버 (실제 배포환경과 동일한 구성)
  nginx:
    image: nginx:alpine
    container_name: lx-chatbot-nginx
    restart: unless-stopped
    ports:
      - "80:80"  # 로컬 테스트용 포트
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - lx-chatbot
    networks:
      - chatbot-network

networks:
  chatbot-network:
    driver: bridge
