events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 로그 설정
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 기본 설정
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # gzip 압축
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    server {
        listen 80;
        server_name localhost;

        # 기본 루트 페이지 (테스트용)
        location / {
            return 200 '<html><body><h1>Nginx Test Server</h1><p><a href="/ai">Go to Chatbot (/ai)</a></p></body></html>';
            add_header Content-Type text/html;
        }

        # 메인 애플리케이션 (단일 location 블록, 경로 보존 + 스트리밍 최적화)
        location /ai {
            proxy_pass              http://lx-chatbot:3000;
            proxy_http_version      1.1;
            proxy_set_header        Host $host;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;

            # 스트리밍 관련 설정
            proxy_buffering         off;      # 버퍼링 비활성화 (SSE/스트리밍)
            proxy_cache             off;
            proxy_request_buffering off;      # 요청 버퍼링 비활성화
            proxy_read_timeout      3600s;    # 긴 스트리밍 대비
            chunked_transfer_encoding on;

            # 헤더 유지 (압축/인코딩 관련)
            # gzip                    off;      # 텍스트 스트리밍 시 지연 방지 고려(전역 gzip on이면 영향 제한적)

            # SSE/스트리밍을 명시적으로 알리기 위한 헤더 전달
            # proxy_set_header        Accept-Encoding "";   # 업스트림에 gzip 비활성 유도
            # proxy_set_header        Connection "keep-alive";
        }

        # 에러 페이지
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }
}
