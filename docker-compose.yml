services:
  # Next.js 챗봇 애플리케이션 (assetPrefix만 설정하여 빌드)
  lx-chatbot:
    image: lx-chatbot/frontend:asset-prefix-only
    container_name: lx-chatbot-frontend
    restart: unless-stopped
    environment:
      # 공유재산 관리 어시스턴트 설정
      - DIFY_API_KEY=${DIFY_API_KEY}
      - DIFY_URL=${DIFY_URL}
    expose:
      - "3000"
    networks:
      - nginx-network

  # 메인 nginx 서버 (domain.co.kr 역할)
  main-nginx:
    image: nginx:alpine
    container_name: main-nginx
    restart: unless-stopped
    ports:
      - "80:80"  # 메인 도메인 포트
    volumes:
      - ./nginx-configs/main-nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - sub-nginx
    networks:
      - nginx-network

  # 서브 nginx 서버 (nlpm-svc2d 서버 역할)
  sub-nginx:
    image: nginx:alpine
    container_name: sub-nginx
    restart: unless-stopped
    expose:
      - "80"
    volumes:
      - ./nginx-configs/sub-nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - lx-chatbot
    networks:
      - nginx-network

networks:
  nginx-network:
    driver: bridge
