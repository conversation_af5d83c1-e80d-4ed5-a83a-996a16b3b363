# Nginx 프록시 테스트 환경 가이드

이 설정은 실제 배포 환경과 동일한 nginx 프록시 구성을 로컬에서 테스트하기 위한 환경입니다.

## 구성 개요

```
브라우저 → nginx (localhost:8080) → lx-chatbot (내부 3000포트)
```

- **nginx**: 프록시 서버 역할, 실제 배포환경과 동일한 설정
- **lx-chatbot**: Next.js 애플리케이션

## 실제 배포환경과의 매핑

| 실제 배포환경 | 로컬 테스트 환경 |
|--------------|-----------------|
| `domain.co.kr/ai` | `localhost:8080/ai` |
| `domain.co.kr/_next/static/` | `localhost:8080/_next/static/` |
| `***********:3003` | `lx-chatbot:3000` |

## 사용법

### 1. 환경 시작

```bash
cd install
docker-compose up -d
```

### 2. 테스트 접속

- **메인 페이지**: http://localhost:8080
- **챗봇 애플리케이션**: http://localhost:8080/ai

### 3. 로그 확인

```bash
# nginx 로그 확인
docker-compose logs nginx

# 챗봇 애플리케이션 로그 확인
docker-compose logs lx-chatbot

# 실시간 로그 모니터링
docker-compose logs -f
```

### 4. 환경 종료

```bash
docker-compose down
```

## 문제 해결

### Next.js 라우팅 문제 확인

1. 브라우저 개발자 도구에서 네트워크 탭 확인
2. 정적 파일 요청이 올바른 경로로 가는지 확인
3. API 요청이 올바르게 프록시되는지 확인

### 일반적인 문제들

1. **404 에러**: nginx 설정에서 경로 매핑 확인
2. **정적 파일 로드 실패**: `/_next/static/` 프록시 설정 확인
3. **API 호출 실패**: `/api/` 프록시 설정 확인

### 설정 수정

nginx 설정을 수정하려면:

1. `install/nginx.conf` 파일 수정
2. nginx 컨테이너 재시작: `docker-compose restart nginx`

## 디버깅 팁

### nginx 컨테이너 내부 접속

```bash
docker exec -it lx-chatbot-nginx sh
```

### 설정 파일 확인

```bash
docker exec lx-chatbot-nginx cat /etc/nginx/nginx.conf
```

### 네트워크 연결 테스트

```bash
# nginx에서 챗봇 서버로의 연결 테스트
docker exec lx-chatbot-nginx wget -qO- http://lx-chatbot:3000/preview
```

## 예상되는 테스트 시나리오

1. `/ai` 경로로 접속했을 때 챗봇이 정상적으로 로드되는지
2. Next.js 정적 파일들이 올바른 경로로 요청되는지
3. API 호출이 정상적으로 프록시되는지
4. 브라우저의 주소창에서 경로가 올바르게 표시되는지

이 환경을 통해 실제 배포환경에서 발생할 수 있는 라우팅 문제를 미리 확인하고 해결할 수 있습니다.
