# Nginx 프록시 환경 테스트 가이드

## 개요
이 가이드는 실제 배포 환경과 동일한 nginx 프록시 구조를 로컬에서 테스트하기 위한 환경 구성 방법을 설명합니다.

## 환경 구조
```
domain.co.kr (메인 nginx) 
    ↓ /nlpm-svc2d/
nlpm-svc2d 서버 (서브 nginx)
    ↓ /ai
챗봇 서비스 (Next.js)
```

최종 경로: `domain.co.kr/nlpm-svc2d/ai` → 챗봇 서비스

## 구성 요소

### 1. Docker Compose 서비스
- **lx-chatbot**: Next.js 챗봇 애플리케이션 (basePath 없이 빌드)
- **main-nginx**: 메인 nginx 서버 (domain.co.kr 역할)
- **sub-nginx**: 서브 nginx 서버 (nlpm-svc2d 서버 역할)

### 2. 네트워크 구성
- 모든 서비스는 `nginx-network` 브리지 네트워크에 연결
- 메인 nginx만 포트 80으로 외부 노출
- 서브 nginx와 챗봇 서비스는 내부 네트워크에서만 접근 가능

## 빌드 및 실행

### 1. basePath 설정된 도커 이미지 빌드
```bash
# basePath와 assetPrefix가 /nlpm-svc2d/ai로 설정된 이미지 빌드
docker build -t lx-chatbot/frontend:with-basepath-fixed \
  --build-arg NEXT_BASE_PATH="/nlpm-svc2d/ai" \
  --build-arg NEXT_PUBLIC_BASE_PATH="/nlpm-svc2d/ai" .
```

### 2. 환경 실행
```bash
docker-compose up -d
```

### 3. 상태 확인
```bash
docker-compose ps
```

## 테스트 방법

### 1. 메인 nginx 서버 테스트
```bash
curl http://localhost/
```
또는 브라우저에서 `http://localhost/` 접속

### 2. 서브 nginx 서버 테스트
```bash
curl http://localhost/nlpm-svc2d/
```
또는 브라우저에서 `http://localhost/nlpm-svc2d/` 접속

### 3. 챗봇 서비스 테스트 (최종 목표)
```bash
curl http://localhost/nlpm-svc2d/ai
```
또는 브라우저에서 `http://localhost/nlpm-svc2d/ai` 접속

### 4. PowerShell에서 테스트 (Windows)
```powershell
Invoke-WebRequest -Uri "http://localhost/nlpm-svc2d/ai" -UseBasicParsing
```

## 예상 결과

### 메인 nginx (`http://localhost/`)
```html
<html><body><h1>Main Nginx Server (domain.co.kr)</h1>
<p><a href="/nlpm-svc2d/ai">Go to Chatbot (/nlpm-svc2d/ai)</a></p>
...
```

### 서브 nginx (`http://localhost/nlpm-svc2d/`)
```html
<html><body><h1>Sub Nginx Server (nlpm-svc2d)</h1>
<p><a href="/ai">Go to Chatbot (/ai)</a></p>
...
```

### 챗봇 서비스 (`http://localhost/nlpm-svc2d/ai`)
Next.js 챗봇 애플리케이션의 HTML 페이지가 정상적으로 로드됨

## 로그 확인

### 컨테이너 로그 확인
```bash
# 메인 nginx 로그
docker-compose logs main-nginx

# 서브 nginx 로그
docker-compose logs sub-nginx

# 챗봇 서비스 로그
docker-compose logs lx-chatbot
```

### 실시간 로그 모니터링
```bash
docker-compose logs -f
```

## 문제 해결

### 1. 컨테이너가 시작되지 않는 경우
```bash
docker-compose down
docker-compose up -d
```

### 2. nginx 설정 변경 후 적용
```bash
docker-compose restart main-nginx sub-nginx
```

### 3. 전체 환경 재시작
```bash
docker-compose down
docker-compose up -d
```

## 환경 정리
```bash
docker-compose down
docker network prune
```

## 주요 설정 파일

- `docker-compose.yml`: 전체 서비스 구성
- `nginx-configs/main-nginx.conf`: 메인 nginx 설정
- `nginx-configs/sub-nginx.conf`: 서브 nginx 설정

## 테스트 결과 확인

### basePath 설정의 중요성
basePath와 assetPrefix를 `/nlpm-svc2d/ai`로 설정한 결과:

✅ **정적 파일 경로 해결**:
- CSS: `/nlpm-svc2d/ai/_next/static/css/...`
- JS: `/nlpm-svc2d/ai/_next/static/chunks/...`
- 이미지: `/nlpm-svc2d/ai/_next/static/media/...`

✅ **API 라우트 경로 해결**:
- API 요청: `POST /nlpm-svc2d/ai/api/dev-chat`

✅ **모든 요청이 올바른 경로로 메인 nginx → 서브 nginx → 챗봇 서비스 순으로 프록시됨**

### 배포 환경 문제 해결
이 테스트를 통해 확인된 사항:
1. **basePath 없이 빌드된 이미지**: 정적 파일과 API가 루트 경로로 요청되어 메인 nginx에서 404 오류 발생
2. **basePath 설정된 이미지**: 모든 요청이 올바른 경로(`/nlpm-svc2d/ai`)로 요청되어 정상 동작

이 환경을 통해 실제 배포 환경과 동일한 nginx 프록시 구조를 로컬에서 테스트할 수 있습니다.
