// Preview 모드 - 로그인 페이지 비활성화
"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function Page() {
	const router = useRouter();

	useEffect(() => {
		// Preview 모드에서는 자동으로 루트 페이지로 리다이렉트
		router.push('/');
	}, [router]);

	return (
		<div className="flex h-screen w-screen items-center justify-center bg-background">
			<div className="text-center">
				<h1 className="text-2xl font-semibold mb-4">Preview Mode</h1>
				<p className="text-gray-600">Redirecting to preview...</p>
			</div>
		</div>
	);
}
