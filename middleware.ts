import { NextResponse } from "next/server";
// Preview 모드 - 인증 관련 import 주석처리
// import { auth } from "./app/(auth)/auth";

export const config = {
    // basePath는 matcher에 포함하지 않습니다. Next가 자동 보정합니다.
    matcher: [
        "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)",
    ],
};

// Preview 모드 - 인증 미들웨어 비활성화, 단순 리다이렉트만 처리
export default function middleware(req: any) {
    const url = req.nextUrl;
    const pathname = url.pathname as string;

    // dev/prod 공통 basePath 인식 (next.config.mjs에서 주입)
    // basePath: Next 내부 설정 사용. URL 기준으로도 파악 가능
    const configuredBasePath = (process.env.NEXT_PUBLIC_BASE_PATH || '').replace(/\/$/, '');
    const basePath = configuredBasePath || (req.nextUrl.basePath || '');

    // 디버깅 로그 (빌드 시 제거되지 않도록)
    console.log(`[MIDDLEWARE] pathname=${pathname}, basePath=${basePath}`);

    // rewrites('/') → '/preview'가 동작하므로, 미들웨어에서는 추가 리다이렉트 없음
    return NextResponse.next();
}
